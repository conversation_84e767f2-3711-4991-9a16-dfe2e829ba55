spring.application.name=customer-contract-service

# JPA configurations
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true

# Allow bean definition overriding
spring.main.allow-bean-definition-overriding=true

# Server
server.port=8083

# Service URLs for Docker environment
customer.service.url=http://customer-service:8081/api/customer
job.service.url=http://job-service:8082/api/job
job-category.service.url=http://job-service:8082/api/job-category

# Management endpoints
management.endpoints.web.exposure.include=*
